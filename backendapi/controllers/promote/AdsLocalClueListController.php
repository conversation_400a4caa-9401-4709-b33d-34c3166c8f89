<?php

namespace backendapi\controllers\promote;

use backendapi\services\promote\AdsLocalClueListService;
use common\helpers\ArrayHelper;
use common\helpers\ResultHelper;
use common\models\promote\AdsLocalClueList;
use auth\services\customer\MobileViewLogService;
use common\enums\CustomerMobileViewLogSourceEnum;
use Exception;
use Yii;

/**
 * 广告本地推线索列管理-后台Api控制器
 */
class AdsLocalClueListController extends \auth\controllers\promote\AdsLocalClueListController
{
    /**
     * @var AdsLocalClueListService
     */
    public $serviceClass = AdsLocalClueListService::class;

    /**
     * 查看手机号
     *
     * @return array|mixed
     */
    public function actionMobileCheck()
    {
        try {
            $params = Yii::$app->request->get();

            $id = ArrayHelper::getValue($params, 'id', 0);
            if (empty($id)) {
                return ResultHelper::json(422, '记录ID不能为空');
            }
            $data = AdsLocalClueList::findOne($id);
            if (!$data) {
                return ResultHelper::json(422, '记录不存在');
            }

            $source = ArrayHelper::getValue($params, 'source');
            if ($source == CustomerMobileViewLogSourceEnum::ADS_LOCAL_CLUE_LIST) {
                // 记录查看手机号日志
                $params['mobile'] = $data->mobile;
                MobileViewLogService::create($params);
            }

            return ResultHelper::json(200, '获取成功', [
                'mobile' => $data->mobile
            ]);
        } catch (Exception $e) {
            return ResultHelper::json(422, $e->getMessage());
        }
    }
}
