<?php

namespace backendapi\services\promote\recharge;

use common\models\backend\Member;
use common\models\promote\AdsTransferMoneyRecord;
use common\queues\RechargeJob;
use Exception;
use Yii;

/**
 * 推广充值
 */
class RechargeService
{
    public $user_name = '';
    public $error_msg = '';
    private $timeRecharge = '';

    public function distribution($params)
    {
        $serialNumber = AdsTransferMoneyRecord::generateSerialNumber();

        try {
            $result = $this->dealParams($params);
            $this->timeLimit();

            $data['amount'] = $this->verificationAmount($result);
            $data['target_advertiser_ids'] = $this->verificationAccount($result);
            $this->isTimeRecharge($result);

            $userId = Member::find()
                ->select('id')
                ->where(['username' => $this->user_name])
                ->scalar();

            $baseData = [
                'serial_number' => $serialNumber,
                'user_id' => $userId ?: 0,
                'user_name' => $this->user_name,
                'target_advertiser_ids' => $data['target_advertiser_ids'],
                'amount' => $data['amount'],
            ];

            // 批量插入数据
            AdsTransferMoneyRecord::batchInsert($baseData);
            // 获取批量插入数据ID
            $advertiserIdToIdMap = AdsTransferMoneyRecord::advertiserIdToIdMap($serialNumber);

            $this->batchAddJobs($baseData, $advertiserIdToIdMap);
        } catch (Exception $e) {
            Yii::error('业务序号：' . $serialNumber . '，接收数据：' . json_encode($params, JSON_UNESCAPED_UNICODE) . '，错误信息：' . $e->getMessage(), 'RechargeService');
            $this->error_msg = $e->getMessage();
        }

        $title = ($this->timeRecharge ? '定时充值' : '推广充值') . ($this->error_msg ? '操作-接收失败' : '操作-接收成功');
        $msg = '业务序号：' . $serialNumber . '<br/>';
        $msg .= '接收时间：' . date('Y-m-d H:i:s') . '<br/>';
        if ($this->error_msg) {
            $msg .= '接收信息：' . ($params['data'] ?: '无') . '<br/>';
            $msg .= '错误信息：' . $this->error_msg;
        } else {
            $msg .= '充值账户：' . implode('、', $data['target_advertiser_ids']) . '<br/>';
            $msg .= '充值金额：' . $data['amount'];
        }
        if (!$this->error_msg && $this->timeRecharge) {
            $msg .= '<br/>定时充值时间：' . date('Y-m-d H:i:s', $this->timeRecharge);
        }
        $title_bg = $this->error_msg ? 'red' : 'green';
        $sendUnionId = RechargeJob::getSendUnionId($this->user_name);
        if ($sendUnionId) {
            RechargeJob::sendMsgToProcess($title, $msg, $title_bg, $sendUnionId);
        } else {
            Yii::error('未找到用户 ' . ($this->user_name ?: '--') . ' 的 UnionId，无法发送消息，消息内容：' . $msg, 'RechargeService');
            Yii::$app->feishuNotice->text('未找到用户 ' . ($this->user_name ?: '--') . ' 的 UnionId，无法发送消息，消息内容：' . PHP_EOL . $msg);
        }

        return true;
    }

    public function timeLimit()
    {
        $currentTime = date('H:i');

        // 定义时间范围
        $startTime = '02:00';
        $endTime = '06:30';

        // 判断当前时间是否在范围内
        if ($currentTime >= $startTime && $currentTime <= $endTime) {
            throw new Exception('“凌晨2点到6点30分”时间段不可充值');
        }
    }

    public function dealParams($params = '')
    {
        if (empty($params)) {
            throw new Exception('充值数据不能为空');
        }

        $this->user_name = $params['user_name'];

        $parts = explode("\n", $params['data']);
        $list = [];
        $keyList = [];
        foreach ($parts as $part) {
            list($key, $value) = explode('：', $part);
            $key = trim($key);
            $value = trim($value);
            $list[$key] = $value;
            $keyList[] = $key;
        }
        $fileds = ['账户ID', '转账金额'];
        $diff = array_diff($fileds, $keyList);
        if (!empty($diff)) {
            throw new Exception('数据格式有误，请认真审查');
        }

        return $list;
    }

    public function verificationAccount($data)
    {
        $target_advertiser_ids = explode('、', $data['账户ID']);
        $target_advertiser_ids = array_filter($target_advertiser_ids, function ($value) {
            return $value !== null && $value !== '';
        });

        $target_advertiser_ids = array_unique($target_advertiser_ids);

        if (empty($target_advertiser_ids)) {
            throw new Exception('账户ID不能为空');
        }

        return $target_advertiser_ids;
    }

    public function verificationAmount($data)
    {
        if ($data['转账金额'] <= 0) {
            throw new Exception('单次充值金额必须大于0');
        }

        return $data['转账金额'];
    }

    public function isTimeRecharge($data)
    {
        if (!isset($data['定时充值'])) {
            return;
        }

        $timeRechargeDate = $data['定时充值'];

        if (empty($timeRechargeDate)) {
            throw new Exception('定时充值时间不能为空');
        }

        //定时充值时间只能在今天和明天之间
        $timeRecharge = strtotime($timeRechargeDate);
        if ($timeRecharge <= time()) {
            throw new Exception('定时充值时间不能小于当前时间');
        }

        $currentDate = date('Y-m-d', time());
        if ($timeRecharge >= strtotime($currentDate . '+2 day')) {
            throw new Exception('定时充值时间只能在今天和明天之间');
        }

        $this->timeRecharge = $timeRecharge;
    }

    public function batchAddJobs($data, $map)
    {
        foreach ($data['target_advertiser_ids'] as $advertiserId) {
            $jobData = [
                'serial_number' => $data['serial_number'],
                'user_id' => $data['user_id'],
                'user_name' => $this->user_name,
                'target_advertiser_id' => $advertiserId,
                'target_advertiser_ids' => $data['target_advertiser_ids'],
                'amount' => $data['amount'],
                'record_id' => $map[$advertiserId] ?? 0,
                'isTimeRecharge' => boolval($this->timeRecharge),
                'execute_time' => $this->timeRecharge ?: time(),
            ];

            RechargeJob::addJob($jobData);
        }
    }
}
