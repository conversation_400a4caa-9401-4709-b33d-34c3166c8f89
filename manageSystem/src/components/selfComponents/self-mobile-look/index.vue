<!--
 * @Author: superLjj <EMAIL>
 * @Date: 2023-03-09 16:59:05
 * @LastEditors: superLjj <EMAIL>
 * @LastEditTime: 2023-03-09 17:21:29
 * @FilePath: \manageSystem\src\components\selfComponents\self-mobile-look\index.vue
 * @Description: 
-->
<template>
  <a-modal v-model="phoneLookBoolean" title="" centered :width="320" destroyOnClose :footer="false" @ok="handleOk">
    <div class="phone-look">{{ phone }}</div>
    <div class="phone-info">客户手机号是公司重要信息，请勿分享给他人</div>
    <div class="btn-modal">
      <a-button key="submit" class="btn" type="primary" @click="handleOk">确定</a-button>
    </div>
  </a-modal>
</template>

<script>
import { theOrderManagementByorder, customerExistingData, adsLocalClueList } from "@/api/api.js";
export default {
  data() {
    return {
      phone: "",
      phoneLookBoolean: false,
    };
  },
  methods: {
    phoneLook(data) {
      const { cus_id, id: order_id } = data;
      if (!data.phoneShow) data.mobile = data.cus_mobile;

      const requestParams = {
        cus_id,
        order_id,
        source: 1
      };

      theOrderManagementByorder.mobileCheck(requestParams).then((res) => {
        if (res.code == 200) {
          data.mobile = res.data.mobile;
          this.phone = res.data.mobile;
          this.phoneLookBoolean = true;
        } else {
          this.$message.error(res.message);
        }
      });
    },
    // 存量客资查看手机号
    phoneLookForCustomer(data) {
      const { id } = data;

      customerExistingData.mobileCheck({ id, source: 2 }).then((res) => {
        if (res.code == 200) {
          // 只设置弹窗显示的手机号，不修改原始数据
          this.phone = res.data.mobile;
          this.phoneLookBoolean = true;
        } else {
          this.$message.error(res.message);
        }
      });
    },
    // 本地推查看手机号
    phoneLookForClue(data) {
      const { id } = data;

      adsLocalClueList.mobileCheck({ id, source: 3 }).then((res) => {
        if (res.code == 200) {
          // 只设置弹窗显示的手机号，不修改原始数据
          this.phone = res.data.mobile;
          this.phoneLookBoolean = true;
        } else {
          this.$message.error(res.message);
        }
      });
    },
    handleOk() {
      this.phoneLookBoolean = false;
    },
  },
};
</script>

<style lang="less" scoped>
// 列表用户信息相关-
/deep/ .ant-modal-body {
  padding: 45px 0 40px;

  .phone-look {
    text-align: center;
    font-family: MiSans-Medium;
    font-size: 34px;
    letter-spacing: -1px;
    color: #333;
  }

  .phone-info {
    font-size: 10px;
    color: #999;
    padding: 3px 30px 15px;
    text-align: center;
  }

  .btn-modal {
    text-align: center;
    margin-top: 10px;

    .btn {
      width: 260px;
      border-radius: 21px !important;
      height: 42px;
      line-height: 42px;
    }
  }
}
</style>