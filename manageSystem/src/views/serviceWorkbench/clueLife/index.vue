<!--
 * @Author: <PERSON>
 * @Date: 2025-08-05
 * @Description: 本地推线索列表页
-->
<template>
  <div>
    <a-card>
      <self-page-header
        @searchQuery="searchQuery"
        @searchReset="searchReset"
      >
        <!-- 查询区域 -->
        <template slot="content">
          <self-col label="手机号">
            <a-input
              placeholder="请输入手机号"
              v-model="queryParam.mobile"
              allowClear
            ></a-input>
          </self-col>
          <self-col label="分配人员">
            <commone-self-principal
              searchKey="keyword"
              :requestFun="allocationUserSelect"
              placeholder="请选择分配人员"
              value_key="name"
              v-model="queryParam.allocation_user_id"
              :isRequest="false"
              :isSearchrRequest="true"
            />
          </self-col>
          <self-col label="留资时间">
            <self-time
              timeRange="month"
              v-model="selfDefaultTime.clueCreated"
              :reset="timeReset"
              :InitializationTime="false"
              :timeKey="{
                start: 'clue_created_at_start',
                end: 'clue_created_at_end',
              }"
            />
          </self-col>
          <self-col label="最近拨打时间">
            <self-time
              timeRange="month"
              v-model="selfDefaultTime.lastRevisit"
              :reset="timeReset"
              :InitializationTime="false"
              :timeKey="{
                start: 'last_revisit_time_start',
                end: 'last_revisit_time_end',
              }"
            />
          </self-col>
          <self-col label="素材ID/计划ID">
            <a-input
              placeholder="请输入素材ID/计划ID"
              v-model="queryParam.material_search"
              allowClear
            ></a-input>
          </self-col>
        </template>
        <!-- 导出 -->
        <template slot="export">
          <export-to-csv
            :dataFormat="dataFormat"
            :query="queryParam"
            fileName="本地推线索"
            btnName="导出"
            :limit="1000"
            :queryParam="queryParam"
            :CommentApi="adsLocalClueListApi.export"
            :header="header"
          ></export-to-csv>
        </template>
      </self-page-header>

      <!-- 数据表格 -->
      <a-table
        ref="table"
        :pagination="ipagination"
        @change="handleTableChange"
        size="middle"
        :scroll="{ x: 1600 }"
        :dataSource="dataSourceFormat"
        :rowKey="record => record.id"
        :columns="columns"
        :loading="loading"
      >
        <!-- 客户信息 -->
        <template slot="customer_info" slot-scope="text, record, index">
          <div class="customer-info">
            <div class="mb5">{{ record.name || '-' }}</div>
            <div>
              {{ record.mobile || '-' }}
              <a
                href="javascript:;"
                @click="handleShow(record)"
                class="ml10"
              >
                显示
              </a>
            </div>
          </div>
        </template>

        <!-- 操作 -->
        <span slot="action" slot-scope="text, record, index">
          <a
            href="javascript:;"
            @click="showLogModal(record)"
            v-has="'ads-local-clue-list-log:index'"
          >
            日志详情
          </a>
        </span>
      </a-table>
    </a-card>

    <!-- 查看手机号弹窗 -->
    <self-mobile-look ref="self-mobile-look" />
  </div>
</template>

<script>
import { JeecgListMixin } from "@/mixins/JeecgListMixin";
import { payRecord, adsLocalClueList } from "@/api/api";
import columns from "./columns";

export default {
  name: "AdsLocalClueListIndex",
  mixins: [JeecgListMixin],
  data() {
    return {
      allocationUserSelect: payRecord.serviceSelect,
      adsLocalClueListApi: adsLocalClueList,
      queryParam: {
        mobile: '',
        allocation_user_id: '',
        clue_created_at_start: '',
        clue_created_at_end: '',
        last_revisit_time_start: '',
        last_revisit_time_end: '',
        material_search: '',
        effective_state: ''
      },
      selfDefaultTime: {},
      columns: columns.adsLocalClueList,
      header: [
        "ID",
        "客户姓名",
        "手机号",
        "阶段信息",
        "分配人员",
        "拨打次数",
        "留资时间",
        "最近拨打时间",
        "渠道",
        "素材ID",
        "账户信息",
        "创建时间"
      ],
      url: {
        list: "/promote/ads-local-clue-list/index",
      },
      ipagination: {
        current: 1,
        total: 0,
        pageSize: 20,
        showSizeChanger: false,
        showTotal: function (total, range) {
          let page = "20/页 共" + total + "条";
          return page;
        },
      },
    };
  },
  computed: {
    dataSourceFormat: function () {
      let d = Object.assign([], this.dataSource.list);
      this.ipagination.total = parseInt(this.dataSource.totalCount);
      return d;
    },
  },
  methods: {
    // 显示手机号
    handleShow(record) {
      this.$refs["self-mobile-look"].phoneLookForClue(record);
    },

    // 显示日志详情模态框
    showLogModal(record) {
      // TODO: 实现日志详情功能
      this.$message.info('日志详情功能待实现');
    },

    // 导出-数据格式
    dataFormat({ list }) {
      let arr = [];
      for (let i = 0; i < list.length; i++) {
        let b = list[i];

        let key = [
          "id",
          "name",
          "mobile",
          "effective_state_text",
          "allocation_user_name",
          "call_num",
          "clue_created_at_text",
          "last_revisit_time_text",
          "promote_source",
          "material_id",
          "account_name",
          "created_at_text",
        ];
        b = this.$utils.fieldCompletion(key, b);
        let nb = this.$pick(b, ...key);
        arr.push(nb);
      }
      return arr;
    },
  },
};
</script>

<style lang="less" scoped>
.customer-info {
  text-align: left;

  .mb5 {
    margin-bottom: 5px;
  }

  a {
    color: #9373ee;
    text-decoration: none;

    &:hover {
      color: #bba0fa;
    }
  }
}

.ml10 {
  margin-left: 10px;
}

/deep/ .ant-form-item {
  margin-bottom: 0px;
}
</style>